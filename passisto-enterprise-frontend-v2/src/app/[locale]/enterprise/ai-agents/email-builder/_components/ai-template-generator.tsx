"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { AlertCircle, Loader2, Sparkles, Wand2, Zap } from "lucide-react";
import { EmailBuilder, type EmailComponent } from "./email-builder";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import axiosInstance from "@/config/axios";
import {
  EMAIL_BUILDER_ENHANCE_DESCRIPTION,
  EMAIL_BUILDER_GENERATE_EMAIL,
} from "@/utils/routes";
import { AxiosResponse } from "axios";
import { useAuth } from "@clerk/nextjs";
import { emailBuilderPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl";
import { UsageLimitDialog } from "@/components/usage-limit-dialog";
import { checkUsageLimit } from "@/lib/checkUsageLimit";


interface EnhanceDescriptionResponse {
  enhancedDescription: string;
}

interface GeneratEmailResponse {
  id: String;
  companyId: String;
  title: string;
  description: string;
  fields: [];
}

interface ResponseItem {
  fields: [];
}
export function AITemplateGenerator() {
  const t  = useTranslations()
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  

  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [generatedTemplate, setGeneratedTemplate] = useState<
    EmailComponent[] | null
  >(null);
  const [generatedTemplateId, setGeneratedTemplateId] = useState<
    String | null
  >(null);
  const [activeTab, setActiveTab] = useState<string>("prompt");
  const [error, setError] = useState<string | null>(null);
  const [enhancedDescription, setEnhancedDescription] = useState<string | null>(
    null
  );
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitInfo, setLimitInfo] = useState({ type: "email", limit: 0 });

  const router = useRouter();

  const enhanceDescription = async () => {
    if (!prompt.trim()) {
      toast(t('empty-description'), {
        description: t('please-enter-a-description-to-enhance'),
      });
      return;
    }

    setIsEnhancing(true);
    setError(null);

    try {
      const token = await getToken();

      // Check usage limit before proceeding
      const usage = await checkUsageLimit("maxEmailAgents", token!);
      if (!usage.canProceed) {
        setLimitInfo({ type: "email", limit: usage.limit || 0 });
        setShowLimitDialog(true);
        setIsEnhancing(false);
        return;
      }

      const response: AxiosResponse<EnhanceDescriptionResponse> =
        await axiosInstance.post(EMAIL_BUILDER_ENHANCE_DESCRIPTION, {
          userInput: prompt,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

      if (!response || !response.status) {
        throw new Error(
          `API error: ${response?.status} ${response?.statusText}`
        );
      }

      const data = response.data;
      setEnhancedDescription(data?.enhancedDescription);

      toast(t('description-enhanced'), {
        description:
          t('your-description-has-been-enhanced-with-additional-details'),
      });
    } catch (error) {
      console.error("Description enhancement error:", error);
      setError(
        error instanceof Error ? error.message : t('an-unknown-error-occurred')
      );
      toast(t('enhancement-failed'), {
        description:
          t('there-was-an-error-enhancing-your-description-please-try-again'),
      });
    } finally {
      setIsEnhancing(false);
    }
  };

  const useEnhancedDescription = () => {
    if (enhancedDescription) {
      setPrompt(enhancedDescription);
      setEnhancedDescription(null);
    }
  };

  const generateTemplate = async () => {
    if (!prompt.trim()) {
      toast(t('empty-prompt'), {
        description: t('please-enter-a-prompt-to-generate-a-template'),
      });
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const token = await getToken();

      // Check usage limit before proceeding
      const usage = await checkUsageLimit("maxEmailAgents", token!);
      if (!usage.canProceed) {
        setLimitInfo({ type: "email", limit: usage.limit || 0 });
        setShowLimitDialog(true);
        setIsGenerating(false);
        return;
      }

      const response: AxiosResponse<GeneratEmailResponse> =
        await axiosInstance.post(
          EMAIL_BUILDER_GENERATE_EMAIL,
          {
            userInput: prompt,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

      if (!response || !response.status) {
        throw new Error(
          `API error: ${response?.status} ${response?.statusText}`
        );
      }

      const data = response.data;
      setGeneratedTemplate(data.fields);
      setGeneratedTemplateId(data.id);
      setActiveTab("preview");

      toast(t('template-generated'), {
        description: t('your-email-template-has-been-created-successfully'),
      });
    } catch (error) {
      console.error("Template generation error:", error);
      setError(
        error instanceof Error ? error.message : t('an-unknown-error-occurred')
      );
      toast(t('generation-failed'), {
        description:
          t('there-was-an-error-generating-your-template-please-try-again'),
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const useTemplate = () => {
    if (generatedTemplate) {
      localStorage.setItem(
        "currentTemplate",
        JSON.stringify(generatedTemplate)
      );
      router.push(`/enterprise/ai-agents/email-builder/editor/${generatedTemplateId}`);
    }
  };

  const editTemplate = () => {
    if (generatedTemplate) {
      localStorage.setItem(
        "currentTemplate",
        JSON.stringify(generatedTemplate)
      );
      router.push(`/enterprise/ai-agents/email-builder/editor/${generatedTemplateId}`);
    }
  };

  return (
    <div className="container mx-auto py-2 px-4">
      <Card className="w-full mx-auto border-0 shadow-none p-2">
        <CardHeader className="flex items-center">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-primary" />
            {t('ai-email-template-generator')}
          </CardTitle>
          <CardDescription>
            {t('describe-the-type-of-email-you-want-to-create-and-our-ai-will-generate-a-template-for-you')}
          </CardDescription>
        </CardHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="prompt">{t('prompt')}</TabsTrigger>
            <TabsTrigger value="preview" disabled={!generatedTemplate}>
              {t('preview')}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="prompt" className="mt-0">
            <CardContent className="pt-6">
              <div className="space-y-4">
                <Textarea
                  placeholder={t('describe-the-email-you-want-to-create-for-example-create-a-welcome-email-for-new-customers-of-my-fitness-company-fitlife-with-a-blue-color-scheme')}
                  className="min-h-[200px] resize-none"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                />

                {enhancedDescription && (
                  <div className="border rounded-md p-4 bg-muted/30">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-medium flex items-center">
                        <Zap className="h-4 w-4 mr-1 text-amber-500" />
                        {t('enhanced-description')}
                      </h3>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={useEnhancedDescription}
                      >
                        {t('use-this')}
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                      {enhancedDescription}
                    </p>
                  </div>
                )}

                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>{t('error')}</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="bg-muted/50 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-2">{t('prompt-tips')}</h3>
                  <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-4">
                    <li>
                      {t('specify-the-type-of-email-welcome-newsletter-promotion-etc')}
                    </li>
                    <li>{t('include-your-company-or-brand-name')}</li>
                    <li>{t('mention-any-color-preferences')}</li>
                    <li>{t('describe-the-tone-formal-casual-exciting')}</li>
                    <li>{t('include-specific-sections-you-want-in-the-email')}</li>
                    <li>
                      {t('mention-if-you-want-special-elements-like-social-icons')}
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>

            <CardFooter className="flex gap-2">
              {emailBuilderPermissions.canEnhanceDescription(backendUser?.permissions || []) && (
              <Button
                variant="outline"
                onClick={enhanceDescription}
                disabled={isEnhancing || !prompt.trim() || isGenerating}
                className="flex-1"
              >
                {isEnhancing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('enhancing')}
                  </>
                ) : (
                  <>
                    <Zap className="mr-2 h-4 w-4" />
                    {t('enhance-description')}
                  </>
                )}
              </Button>
              )}
              {emailBuilderPermissions.canGenerateTemplate(backendUser?.permissions || []) && (
              <Button
                onClick={generateTemplate}
                disabled={isGenerating || !prompt.trim() || isEnhancing}
                className="flex-1"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('generating')}
                  </>
                ) : (
                  <>
                    <Wand2 className="mr-2 h-4 w-4" />
                    {t('generate-template')}
                  </>
                )}
              </Button>
              )}
            </CardFooter>
          </TabsContent>

          <TabsContent value="preview" className="mt-0">
            {generatedTemplate && (
              <>
                <div className="h-[500px] overflow-auto border rounded-md">
                  <EmailBuilder
                    readOnly={true}
                    initialComponents={generatedTemplate}
                  />
                </div>

                <CardFooter className="flex gap-4 pt-6">
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab("prompt")}
                    className="flex-1"
                  >
                    {t('regenerate')}
                  </Button>
                  <Button onClick={editTemplate} className="flex-1">
                    {t('edit-template')}
                  </Button>
                  <Button
                    variant="default"
                    onClick={useTemplate}
                    className="flex-1"
                  >
                    {t('use-template')}
                  </Button>
                </CardFooter>
              </>
            )}
          </TabsContent>
        </Tabs>
      </Card>

      <UsageLimitDialog
        open={showLimitDialog}
        onOpenChange={setShowLimitDialog}
        type="email"
        limit={limitInfo.limit}
      />
    </div>
  );
}
