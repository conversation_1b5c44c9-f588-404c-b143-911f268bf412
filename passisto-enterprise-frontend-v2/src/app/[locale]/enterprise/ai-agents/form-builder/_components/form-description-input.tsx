"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Wand2, <PERSON><PERSON><PERSON>, Loader2, Check } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@clerk/nextjs";
import {
  ENHANCE_DESCRIPTION_PROMPT,
  GENERATE_TEMPLATE_PROMPT,
} from "../_data/PROMPTS";
import axiosInstance from "@/config/axios";
import {
  FORM_BUILDER_ENHANCE_DESCRIPTION,
  FORM_BUILDER_GENERATE_FORM,
} from "@/utils/routes";
import type { FormTemplate, StoredForm } from "../_lib/types";
import { formBuilderPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl";
import { UsageLimitDialog } from "@/components/usage-limit-dialog";
import { checkUsageLimit } from "@/lib/checkUsageLimit";

export function FormDescriptionInput() {
  const t = useTranslations();
  const router = useRouter();
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  const [formIntent, setFormIntent] = useState("");
  const [enhancedDescription, setEnhancedDescription] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // Usage limit dialog state
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitInfo, setLimitInfo] = useState({ type: "form", limit: 0 });

  const enhanceDescription = async () => {
    if (!formIntent.trim()) {
      setError(t("please-enter-a-form-description-first"));
      return;
    }

    setError(null);
    setEnhancedDescription("");
    setIsEnhancing(true);

    try {
      const token = await getToken();

      // Check usage limit before proceeding
      const usage = await checkUsageLimit("maxFormAgents", token!);
      if (!usage.canProceed) {
        setLimitInfo({ type: "form", limit: usage.limit || 0 });
        setShowLimitDialog(true);
        setIsEnhancing(false);
        return;
      }

      const prompt = ENHANCE_DESCRIPTION_PROMPT(formIntent);
      const response = await axiosInstance.post(
        FORM_BUILDER_ENHANCE_DESCRIPTION,
        { userInput: prompt },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = response.data.enhancedDescription;
      setEnhancedDescription(result);
    } catch (err) {
      console.error("Error enhancing description:", err);
      setError(t("failed-to-enhance-description-please-try-again"));
    } finally {
      setIsEnhancing(false);
    }
  };

  const useEnhancedDescription = () => {
    setFormIntent(enhancedDescription);
    setEnhancedDescription("");
    setSuccess(t("enhanced-description-applied"));

    setTimeout(() => {
      setSuccess(null);
    }, 3000);
  };

  const generateForm = async () => {
    if (!formIntent.trim()) {
      setError(t("please-describe-your-form-purpose"));
      return;
    }

    setError(null);
    setIsGenerating(true);

    try {
      const token = await getToken();

      // Check usage limit before proceeding
      const usage = await checkUsageLimit("maxFormAgents", token!);
      if (!usage.canProceed) {
        setLimitInfo({ type: "form", limit: usage.limit || 0 });
        setShowLimitDialog(true);
        setIsGenerating(false);
        return;
      }

      const prompt = GENERATE_TEMPLATE_PROMPT(formIntent);

      const response = await axiosInstance.post(
        FORM_BUILDER_GENERATE_FORM,
        { userInput: prompt },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = response.data;
      if (result && result.formTemplate) {
        const parsedTemplate = result.formTemplate as FormTemplate;
        const formId = parsedTemplate.id;

        const newForm: StoredForm = {
          id: formId,
          companyId: "company-id",
          title: parsedTemplate.title,
          description: parsedTemplate.description,
          fields: parsedTemplate.fields.map((field) => ({
            ...field,
            id: `field-${Date.now()}-${Math.random()
              .toString(36)
              .substr(2, 9)}`,
          })),
          brandColor: "#4f46e5",
          logo: "",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        const existingForms = localStorage.getItem("forms");
        const forms = existingForms ? JSON.parse(existingForms) : [];
        forms.push(newForm);
        localStorage.setItem("forms", JSON.stringify(forms));

        window.dispatchEvent(new Event("storage"));

        router.push(`/enterprise/ai-agents/form-builder/editor/${formId}`);
      } else {
        setError("Failed to generate form. Please try again.");
      }
    } catch (err) {
      console.error("Error generating form:", err);
      setError(
        t("an-error-occurred-while-generating-the-form-please-try-again")
      );
    } finally {
      setIsGenerating(false);
    }
  };
  return (
    <>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>{t("describe-your-form")}</CardTitle>
          <CardDescription>
            {t(
              "tell-us-what-kind-of-form-you-need-and-our-ai-will-generate-it-for-you"
            )}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <Textarea
            placeholder={t(
              "describe-your-form-purpose-e-g-job-application-form-for-a-marketing-position-event-registration-form-for-a-conference"
            )}
            value={formIntent}
            onChange={(e) => setFormIntent(e.target.value)}
            className="resize-none min-h-[150px]"
            rows={5}
          />

          {error && <p className="text-red-500 text-sm mt-2">{error}</p>}

          {success && (
            <Alert className="mt-2 bg-green-50 border-green-200">
              <AlertDescription className="flex items-center text-green-600">
                <Check className="h-4 w-4 mr-2" />
                {success}
              </AlertDescription>
            </Alert>
          )}

          <div className="bg-muted/50 p-4 rounded-md">
            <h3 className="text-sm font-medium mb-2">{t("prompt-tips")}</h3>
            <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-4">
              <li>
                {t(
                  "specify-the-purpose-of-the-form-job-application-event-registration-feedback-etc"
                )}
              </li>
              <li>
                {t("include-your-company-or-brand-name-for-customization")}
              </li>
              <li>
                {t("mention-required-fields-e-g-name-email-phone-number")}
              </li>
              <li>
                {t(
                  "describe-any-conditional-logic-e-g-show-extra-fields-based-on-selection"
                )}
              </li>
              <li>
                {t(
                  "specify-form-actions-e-g-email-responses-save-to-database-integrate-with-apis"
                )}
              </li>
              <li>
                {t(
                  "mention-if-you-need-file-uploads-e-g-resumes-images-documents"
                )}
              </li>
              <li>
                {t(
                  "describe-validation-rules-e-g-required-fields-email-format-character-limits"
                )}
              </li>
              <li>
                {t(
                  "indicate-if-you-want-branding-elements-like-logos-and-theme-colors"
                )}
              </li>
              <li>
                {t(
                  "specify-if-you-need-auto-confirmation-emails-or-notifications"
                )}
              </li>
            </ul>
          </div>

          {enhancedDescription && (
            <div className="border rounded-md p-4 bg-primary/5">
              <p className="text-sm font-medium mb-2">
                {t("enhanced-description-0")}
              </p>
              <p className="text-sm mb-3">{enhancedDescription}</p>
              <Button size="sm" onClick={useEnhancedDescription}>
                {t("use-this-description")}
              </Button>
            </div>
          )}
        </CardContent>

        <CardFooter className="flex gap-2">
          {formBuilderPermissions.canCreateForm(
            backendUser?.permissions || []
          ) && (
            <Button
              variant="outline"
              className="flex-1"
              onClick={enhanceDescription}
              disabled={isEnhancing || isGenerating || !formIntent.trim()}
            >
              {isEnhancing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Enhancing...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  {t("enhance-description-0")}
                </>
              )}
            </Button>
          )}

          {formBuilderPermissions.canCreateForm(
            backendUser?.permissions || []
          ) && (
            <Button
              className="flex-1"
              onClick={generateForm}
              disabled={isGenerating || isEnhancing || !formIntent.trim()}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t("generating-form")}
                </>
              ) : (
                <>
                  <Wand2 className="mr-2 h-4 w-4" />
                  {t("generate-form")}
                </>
              )}
            </Button>
          )}
        </CardFooter>
      </Card>
      <UsageLimitDialog
        open={showLimitDialog}
        onOpenChange={setShowLimitDialog}
        type="form"
        limit={limitInfo.limit}
      />
    </>
  );
}
