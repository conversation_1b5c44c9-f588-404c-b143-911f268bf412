"use client";

import React, { createContext, useContext, ReactNode } from 'react';
import { useChatSession } from '@/hooks/useChatSession';
import { ChatMessage, ChatSession } from '@/services/chatbotApi';

interface DeleteSessionResult {
  success: boolean;
  newSessionId?: string;
}

interface ChatSessionContextType {
  sessionId: string;
  messages: ChatMessage[];
  sessions: ChatSession[];
  loading: boolean;
  sendingMessage: boolean;
  switchingSession: boolean;
  error: string | null;
  sendMessage: (message: string) => Promise<void>;
  createNewChat: () => string;
  switchSession: (id: string) => void;
  deleteSession: (id: string) => Promise<DeleteSessionResult>;
  fetchChatHistory: () => Promise<void>;
  fetchUserSessions: () => Promise<void>;
  showLimitDialog: boolean;
  setShowLimitDialog: (show: boolean) => void;
  limitInfo: { type: string; limit: number };
}

const ChatSessionContext = createContext<ChatSessionContextType | undefined>(undefined);

export function ChatSessionProvider({ children }: { children: ReactNode }) {
  const chatSession = useChatSession();

  return (
    <ChatSessionContext.Provider value={chatSession}>
      {children}
    </ChatSessionContext.Provider>
  );
}

export function useChatSessionContext() {
  const context = useContext(ChatSessionContext);
  if (context === undefined) {
    throw new Error('useChatSessionContext must be used within a ChatSessionProvider');
  }
  return context;
}
