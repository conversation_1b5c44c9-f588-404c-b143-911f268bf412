"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertCircle, Loader2 } from "lucide-react";
import { useAuth } from "@clerk/nextjs";
import { useState } from "react";
import { toast } from "sonner";
import axiosInstance from "@/config/axios";
import { CREATE_STRIPE_PORTAL_SESSION } from "@/utils/routes";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl"; // Import useTranslations

interface UsageLimitDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: string;
  limit: number;
}

export function UsageLimitDialog({
  open,
  onOpenChange,
  type,
  limit,
}: UsageLimitDialogProps) {
  const { getToken } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();
  const t = useTranslations("UsageLimitDialog"); // Initialize useTranslations

  // Check if the user is a candidate based on the URL path
  const isCandidate = pathname?.includes('/public-page/interview/');

  const handleUpgrade = async () => {
    setIsLoading(true);
    try {
      const token = await getToken();
      const response = await axiosInstance.post(
        CREATE_STRIPE_PORTAL_SESSION,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.url) {
        window.location.href = response.data.url;
      }
    } catch (error) {
      console.error("Failed to create portal session:", error);
      toast.error(t("toastFailedAccess")); // Use translation key
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            {isCandidate ? t("candidateTitle") : t("userTitle")} {/* Use translation keys */}
          </DialogTitle>
          <DialogDescription className="pt-2">
            {isCandidate ? (
              <div className="space-y-2">
                <p>{t("candidateMessage1")}</p> {/* Use translation key */}
                <p>{t("candidateMessage2")}</p> {/* Use translation key */}
              </div>
            ) : (
              <p>
                {t("userMessage", { limit, type: t(type, { count: limit }), unit: t(type === "interview" ? "minutes" : type === "chat messages" ? "messages" : "genericUnit")})}
              </p>
            )}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex gap-2 sm:gap-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("closeButton")} {/* Use translation key */}
          </Button>
          {!isCandidate && (
            <Button onClick={handleUpgrade} disabled={isLoading}>
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {t("processingButton")} {/* Use translation key */}
                </div>
              ) : (
                t("upgradeButton") // Use translation key
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}