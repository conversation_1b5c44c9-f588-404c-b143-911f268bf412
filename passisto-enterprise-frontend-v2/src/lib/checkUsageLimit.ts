import { getTranslations } from 'next-intl/server';
import axiosInstance from "@/config/axios";
import { CHECK_USAGE_LIMIT, CHECK_USAGE_LIMIT_BY_COMPANY_ID } from "../utils/routes";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

interface UsageResponse {
  success: boolean;
  isExceeded: boolean;
  remaining: number;
  currentUsage: number;
  limit: number;
}

interface UsageLimitResult {
  canProceed: boolean;
  limit?: number;
  remaining?: number;
}

export async function checkUsageLimit(
  key: string, // now accepts any feature key, e.g. "maxEmailAgents"
  token: string,
  warnThreshold: number = 10 // optional: warn when remaining <= threshold
): Promise<UsageLimitResult> {
  const t = getTranslations("usageLimit");
  try {
    const response = await axiosInstance.post<UsageResponse>(
      CHECK_USAGE_LIMIT,
      { key },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (response.data.isExceeded) {
      toast.error(`Usage limit exceeded for this feature.`);
      return {
        canProceed: false,
        limit: response.data.limit,
      };
    }

    if (response.data.remaining <= warnThreshold) {
      toast.warning(
        // `Usage limit warning: You have ${response.data.remaining} remaining for this feature in your current plan.`
        "asdasdasd"
      );
    }

    return {
      canProceed: true,
      remaining: response.data.remaining,
      limit: response.data.limit,
    };
  } catch (error: any) {
    const message =
      error.response?.data?.message || "Failed to check usage limit";
    toast.error("Error", { description: message });
    return { canProceed: false };
  }
}

export async function checkUsageLimitByCompanyId(
  key: string, // now accepts any feature key
  companyId: string,
  warnThreshold: number = 10
): Promise<UsageLimitResult> {
  try {
    const response = await axiosInstance.post<UsageResponse>(
      CHECK_USAGE_LIMIT_BY_COMPANY_ID,
      { key, companyId },
    );
    if (response.data.isExceeded) {
      toast.error(`Usage limit exceeded for this feature.`);
      return {
        canProceed: false,
        limit: response.data.limit,
      };
    }

    if (response.data.remaining <= warnThreshold) {
      toast.warning(
        `Usage limit warning: You have ${response.data.remaining} remaining for this feature in your current plan.`
      );
    }

    return {
      canProceed: true,
      remaining: response.data.remaining,
      limit: response.data.limit,
    };
  } catch (error: any) {
    const message =
      error.response?.data?.message || "Failed to check usage limit";
    toast.error("Error", { description: message });
    return { canProceed: false };
  }
}
